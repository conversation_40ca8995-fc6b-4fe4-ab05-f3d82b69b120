//  write tests for HeatmapLevelItem
import React from 'react';
import { render, screen } from 'tests/test-utils';
import { HeatmapLevelItem } from './HeatmapLevelItem';

describe('<HeatmapLevelItem />', () => {
    const heatmapLevelItemProps = {
        title: 'Not useful',
        scoreRange: 'Score range: 0-19%',
        description: 'Issues are severely lacking in essential information or have been inactive for too long.',
        className: 'bg-red-500',
    };

    it('renders correct title', () => {
        render(
            <HeatmapLevelItem {...heatmapLevelItemProps} />
        );

        expect(screen.getByText('Not useful')).toBeInTheDocument();
    });

    describe('when user hovers over the item', () => {
        describe("when screen is large", () => {
            it('shows the popover with correct content', async () => {
                const { user } = render(
                    <HeatmapLevelItem {...heatmapLevelItemProps} />
                );

                user.hover(screen.getByText('Not useful'));

                expect(await screen.findByText(heatmapLevelItemProps.title)).toBeInTheDocument();
                expect(await screen.findByText(heatmapLevelItemProps.scoreRange)).toBeInTheDocument();
                expect(await screen.findByText(heatmapLevelItemProps.description)).toBeInTheDocument();

            });
        });

        describe("when screen is small", () => {
            it('does not show the popover on pointer over', async () => {
                window.matchMedia = jest.fn().mockImplementation(() => ({
                    matches: false,
                    addListener: jest.fn(),
                    removeListener: jest.fn(),
                }));
                const { user } = render(
                    <HeatmapLevelItem {...heatmapLevelItemProps} />
                );

                user.hover(screen.getByText('Not useful'));

                expect(screen.queryByText(heatmapLevelItemProps.title)).not.toBeInTheDocument();
            });
        });

    });
});
